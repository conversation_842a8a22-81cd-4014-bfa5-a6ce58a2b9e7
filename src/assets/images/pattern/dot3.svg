<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="200" height="200" viewBox="0 0 200 200" xml:space="preserve">
<desc>Created with Fabric.js 4.0.0-beta.8</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 100.5 100.5)"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;"  x="-100" y="-100" rx="0" ry="0" width="200" height="200" />
</g>
<g transform="matrix(0.13 0 0 0.13 100 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 110 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 110 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 90 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 90 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 100 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 110 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 100 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.12 0 0 0.12 90 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 110 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 90 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 100 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 100 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 90 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 110 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 110 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 90 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 100 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 100 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 90 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 110 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 80 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 130 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 120 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.11 0 0 0.11 70 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 130 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 70 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 120 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 80 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 110 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 90 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 100 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 100 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 90 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 110 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 80 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 120 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 70 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 140 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 130 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.1 0 0 0.1 60 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 140 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 60 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 130 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 70 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 120 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 80 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 110 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 90 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 100 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 100 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 90 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 110 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 80 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 120 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 70 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 130 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 60 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 140 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 140 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 60 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 130 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 70 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 120 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 80 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 110 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 90 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 100 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 100 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 90 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 110 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 80 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 120 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 70 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 130 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 60 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 140 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 50 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 160 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 150 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.09 0 0 0.09 40 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 160 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 40 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 150 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 50 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 140 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 60 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 130 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 70 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 120 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 80 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 110 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 90 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 100 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 100 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 90 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 110 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 80 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 120 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 70 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 130 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 60 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 140 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 50 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 150 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 40 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 170 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 160 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.08 0 0 0.08 30 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 170 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 30 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 160 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 40 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 150 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 50 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 140 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 60 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 130 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 70 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 120 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 80 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 110 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 90 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 100 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 100 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 90 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 110 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 80 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 120 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 70 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 130 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 60 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 140 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 50 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 150 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 40 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 160 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 30 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 170 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 170 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 30 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 160 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 40 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 150 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 50 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 140 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 60 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 130 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 70 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 120 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 80 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 110 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 90 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 100 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 100 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 100)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 90 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 90)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 110 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 110)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 80 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 80)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 120 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 120)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 70 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 70)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 130 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 130)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 60 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 60)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 140 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 140)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 50 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 50)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 150 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 150)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 40 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 40)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 160 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 160)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 30 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 30)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 170 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 170)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 20 10)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 190 20)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 180 190)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
<g transform="matrix(0.07 0 0 0.07 10 180)"  >
<circle style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(192,199,220); fill-rule: nonzero; opacity: 1;"  cx="0" cy="0" r="16" />
</g>
</svg>