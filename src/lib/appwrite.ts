import { Client, Account, Databases, Storage, Functions, Realtime } from 'appwrite'

// Appwrite configuration
const client = new Client()

client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT!)
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!)

// Initialize Appwrite services
export const account = new Account(client)
export const databases = new Databases(client)
export const storage = new Storage(client)
export const functions = new Functions(client)
export const realtime = new Realtime(client)

// Database and Collection IDs (to be created in Appwrite console)
export const DATABASE_ID = 'hvppy-central-db'

export const COLLECTIONS = {
  USERS: 'users',
  CREATORS: 'creators',
  PERSONAS: 'personas',
  CONTENT: 'content',
  POSTS: 'posts',
  MOODS: 'moods',
  REACTIONS: 'reactions',
  MEMORIES: 'memories',
  FOLLOWERS: 'followers',
  NOTIFICATIONS: 'notifications',
} as const

// Storage Bucket IDs
export const BUCKETS = {
  AVATARS: 'avatars',
  CONTENT_MEDIA: 'content-media',
  THUMBNAILS: 'thumbnails',
  MEMORIES: 'memories',
} as const

// Helper functions
export const getFileUrl = (bucketId: string, fileId: string) => {
  return `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`
}

export const getFilePreview = (bucketId: string, fileId: string, width = 400, height = 400) => {
  return `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/preview?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}&width=${width}&height=${height}`
}

// Mood types for emotional AI
export const MOOD_TYPES = {
  HAPPY: 'happy',
  CHILL: 'chill',
  HEARTBROKEN: 'heartbroken',
  INSPIRED: 'inspired',
  ENERGETIC: 'energetic',
  PEACEFUL: 'peaceful',
  NOSTALGIC: 'nostalgic',
  EXCITED: 'excited',
} as const

export type MoodType = typeof MOOD_TYPES[keyof typeof MOOD_TYPES]

// Content types
export const CONTENT_TYPES = {
  MUSIC: 'music',
  VIDEO: 'video',
  IMAGE: 'image',
  TEXT: 'text',
  STORY: 'story',
  LIVE: 'live',
  MEMORY: 'memory',
} as const

export type ContentType = typeof CONTENT_TYPES[keyof typeof CONTENT_TYPES]

// User roles
export const USER_ROLES = {
  FAN: 'fan',
  CREATOR: 'creator',
  ADMIN: 'admin',
} as const

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES]

export default client
