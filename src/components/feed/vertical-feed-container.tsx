"use client"

import React, { useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { VerticalFeedContainerProps, InteractionData } from '@/types/feed'
import { useFeedData } from '@/hooks/feed/use-feed-data'
import { useInfiniteScroll } from '@/hooks/feed/use-infinite-scroll'
import { useContentInteractions } from '@/hooks/feed/use-content-interactions'
// import { ContentCard } from './content-card'
// import { FeedNavigation } from './feed-navigation'
// import { MoodSelector } from './mood-selector'
import { useMoodFilter } from '@/hooks/feed/use-mood-filter'
import { Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

export function VerticalFeedContainer({
  feedType,
  filters,
  onItemChange,
  onInteraction,
  className,
  autoPlay = true,
}: VerticalFeedContainerProps) {
  const {
    items,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    updateFilters,
  } = useFeedData(feedType, filters)

  const {
    selectedMoods,
    setSelectedMoods,
    moodCounts,
    clearMoods,
  } = useMoodFilter()

  const {
    react,
    unreact,
    toggleMemory,
    share,
  } = useContentInteractions()

  const handleItemChange = useCallback((index: number) => {
    const item = items[index]
    if (item) {
      onItemChange?.(item, index)
      
      // Load more items when approaching the end
      if (index >= items.length - 3 && hasMore && !loading) {
        loadMore()
      }
    }
  }, [items, onItemChange, hasMore, loading, loadMore])

  const {
    containerRef,
    currentIndex,
    isScrolling,
    scrollToIndex,
    scrollDirection,
  } = useInfiniteScroll({
    onItemChange: handleItemChange,
    autoAdvance: false, // Disable auto-advance for user control
  })

  // Handle interactions
  const handleInteraction = useCallback((interaction: InteractionData) => {
    onInteraction?.(interaction)
    
    switch (interaction.type) {
      case 'LIKE':
      case 'LOVE':
      case 'FIRE':
      case 'MIND_BLOWN':
      case 'VIBE':
      case 'MOOD_MATCH':
        react(interaction.postId, interaction.type, interaction.mood)
        break
    }
  }, [onInteraction, react])

  // Handle mood filter changes
  const handleMoodChange = useCallback((moods: string[]) => {
    setSelectedMoods(moods as any)
    updateFilters({ moods: moods as any })
  }, [setSelectedMoods, updateFilters])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.target !== document.body) return

      switch (event.key) {
        case ' ': // Spacebar
          event.preventDefault()
          // Toggle play/pause for current item
          break
        case 'r':
          event.preventDefault()
          refresh()
          break
        case 'm':
          event.preventDefault()
          clearMoods()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [refresh, clearMoods])

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <p className="text-destructive mb-4">Failed to load feed: {error}</p>
        <Button onClick={refresh} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("relative h-screen bg-black overflow-hidden", className)}>
      {/* Feed Navigation - TODO: Implement */}
      <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/50 to-transparent">
        <div className="p-4 text-white">
          Feed: {feedType}
        </div>
      </div>

      {/* Mood Selector - TODO: Implement */}
      {selectedMoods.length > 0 && (
        <div className="absolute top-16 left-0 right-0 z-20 bg-gradient-to-b from-black/30 to-transparent">
          <div className="p-4 text-white">
            Moods: {selectedMoods.join(', ')}
          </div>
        </div>
      )}

      {/* Main Feed Container */}
      <div
        ref={containerRef}
        className="h-full overflow-y-auto snap-y snap-mandatory scrollbar-hide"
        style={{ scrollBehavior: 'smooth' }}
        tabIndex={0}
        role="feed"
        aria-label={`${feedType} feed`}
      >
        {items.map((item, index) => (
          <div
            key={item.id}
            className="h-screen snap-start snap-always"
            role="article"
            aria-label={`Post ${index + 1} of ${items.length}`}
          >
            {/* TODO: Implement ContentCard */}
            <div className="h-full bg-gray-900 flex items-center justify-center text-white">
              <div className="text-center">
                <h3 className="text-xl mb-2">{item.post.title || 'Untitled'}</h3>
                <p className="text-sm opacity-70">Post {index + 1}</p>
                <p className="text-xs mt-2">Active: {index === currentIndex ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {loading && (
          <div className="h-screen flex items-center justify-center snap-start">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}

        {/* End of feed indicator */}
        {!hasMore && items.length > 0 && (
          <div className="h-screen flex items-center justify-center snap-start">
            <div className="text-center text-white/70">
              <p className="text-lg mb-2">You've reached the end!</p>
              <Button onClick={refresh} variant="outline" size="sm">
                Refresh Feed
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Scroll indicators */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
        <div className="flex flex-col space-y-1">
          {items.slice(Math.max(0, currentIndex - 2), currentIndex + 3).map((_, relativeIndex) => {
            const actualIndex = Math.max(0, currentIndex - 2) + relativeIndex
            const isActive = actualIndex === currentIndex
            
            return (
              <button
                key={actualIndex}
                onClick={() => scrollToIndex(actualIndex)}
                className={cn(
                  "w-1 h-8 rounded-full transition-all duration-200",
                  isActive 
                    ? "bg-white" 
                    : "bg-white/30 hover:bg-white/50"
                )}
                aria-label={`Go to item ${actualIndex + 1}`}
              />
            )
          })}
        </div>
      </div>

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 z-20 bg-black/50 text-white text-xs p-2 rounded">
          <div>Index: {currentIndex + 1}/{items.length}</div>
          <div>Scrolling: {isScrolling ? 'Yes' : 'No'}</div>
          <div>Direction: {scrollDirection || 'None'}</div>
          <div>Has More: {hasMore ? 'Yes' : 'No'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  )
}
