// Main feed components
export { VerticalFeedContainer } from './vertical-feed-container'

// Individual component exports (to be implemented)
// export { ContentCard } from './content-card'
// export { FeedControls } from './feed-controls'
// export { MoodSelector } from './mood-selector'
// export { CreatorInfo } from './creator-info'
// export { FeedNavigation } from './feed-navigation'

// Hook exports
export { useFeedData } from '@/hooks/feed/use-feed-data'
export { useInfiniteScroll } from '@/hooks/feed/use-infinite-scroll'
export { useMoodFilter } from '@/hooks/feed/use-mood-filter'
export { useContentInteractions } from '@/hooks/feed/use-content-interactions'
export { useVideoPlayer } from '@/hooks/feed/use-video-player'

// Store exports
export { useFeedStore } from '@/lib/stores/feed-store'
export { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
export { useInteractionStore } from '@/lib/stores/interaction-store'
export { usePlayerStore } from '@/lib/stores/player-store'

// Type exports
export type {
  FeedItem,
  FeedType,
  FeedFilters,
  FeedPreferences,
  InteractionData,
  VideoPlayerState,
  VerticalFeedContainerProps,
  ContentCardProps,
  FeedControlsProps,
  MoodSelectorProps,
  CreatorInfoProps,
  FeedNavigationProps,
} from '@/types/feed'
