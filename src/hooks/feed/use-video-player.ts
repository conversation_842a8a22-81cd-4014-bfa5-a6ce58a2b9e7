import { useEffect, useCallback, useRef } from 'react'
import { usePlayerStore, selectIsCurrentPost } from '@/lib/stores/player-store'
import { useUserPreferencesStore } from '@/lib/stores/user-preferences-store'
import { UseVideoPlayerReturn } from '@/types/feed'

interface UseVideoPlayerOptions {
  postId: string
  autoPlay?: boolean
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onTimeUpdate?: (currentTime: number) => void
}

export function useVideoPlayer(options: UseVideoPlayerOptions): UseVideoPlayerReturn {
  const {
    postId,
    autoPlay = false,
    onPlay,
    onPause,
    onEnded,
    onTimeUpdate,
  } = options

  const playerRef = useRef<HTMLVideoElement | HTMLAudioElement>(null)
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout>()

  const {
    playerState,
    setCurrentPost,
    play: globalPlay,
    pause: globalPause,
    togglePlay,
    setMuted,
    toggleMute,
    setVolume,
    setCurrentTime,
    setDuration,
    setBuffering,
    setPlaybackRate,
    registerPlayer,
    unregisterPlayer,
    playPost,
  } = usePlayerStore()

  const { autoPlay: userAutoPlay } = useUserPreferencesStore()
  const isCurrentPost = selectIsCurrentPost(postId)(usePlayerStore.getState())

  // Register player element
  useEffect(() => {
    if (playerRef.current) {
      registerPlayer(postId, playerRef.current)
      
      return () => {
        unregisterPlayer(postId)
      }
    }
  }, [postId, registerPlayer, unregisterPlayer])

  // Set up player event listeners
  useEffect(() => {
    const player = playerRef.current
    if (!player) return

    const handleLoadedMetadata = () => {
      if (isCurrentPost) {
        setDuration(player.duration || 0)
      }
    }

    const handleTimeUpdate = () => {
      if (isCurrentPost) {
        setCurrentTime(player.currentTime)
        onTimeUpdate?.(player.currentTime)
      }
    }

    const handlePlay = () => {
      if (isCurrentPost) {
        onPlay?.()
      }
    }

    const handlePause = () => {
      if (isCurrentPost) {
        onPause?.()
      }
    }

    const handleEnded = () => {
      if (isCurrentPost) {
        onEnded?.()
      }
    }

    const handleWaiting = () => {
      if (isCurrentPost) {
        setBuffering(true)
      }
    }

    const handleCanPlay = () => {
      if (isCurrentPost) {
        setBuffering(false)
      }
    }

    const handleError = (event: Event) => {
      console.error('Player error:', event)
      if (isCurrentPost) {
        setBuffering(false)
      }
    }

    player.addEventListener('loadedmetadata', handleLoadedMetadata)
    player.addEventListener('timeupdate', handleTimeUpdate)
    player.addEventListener('play', handlePlay)
    player.addEventListener('pause', handlePause)
    player.addEventListener('ended', handleEnded)
    player.addEventListener('waiting', handleWaiting)
    player.addEventListener('canplay', handleCanPlay)
    player.addEventListener('error', handleError)

    return () => {
      player.removeEventListener('loadedmetadata', handleLoadedMetadata)
      player.removeEventListener('timeupdate', handleTimeUpdate)
      player.removeEventListener('play', handlePlay)
      player.removeEventListener('pause', handlePause)
      player.removeEventListener('ended', handleEnded)
      player.removeEventListener('waiting', handleWaiting)
      player.removeEventListener('canplay', handleCanPlay)
      player.removeEventListener('error', handleError)
    }
  }, [isCurrentPost, setDuration, setCurrentTime, setBuffering, onPlay, onPause, onEnded, onTimeUpdate])

  // Auto-play logic
  useEffect(() => {
    if (isCurrentPost && autoPlay && userAutoPlay && playerRef.current) {
      const playPromise = playerRef.current.play()
      if (playPromise) {
        playPromise.catch(error => {
          console.warn('Auto-play failed:', error)
        })
      }
    }
  }, [isCurrentPost, autoPlay, userAutoPlay])

  // Play function for this specific post
  const play = useCallback(() => {
    if (playerRef.current) {
      setCurrentPost(postId)
      const playPromise = playerRef.current.play()
      if (playPromise) {
        playPromise.catch(error => {
          console.error('Play failed:', error)
        })
      }
    }
  }, [postId, setCurrentPost])

  // Pause function for this specific post
  const pause = useCallback(() => {
    if (playerRef.current && isCurrentPost) {
      playerRef.current.pause()
    }
  }, [isCurrentPost])

  // Seek to specific time
  const seek = useCallback((time: number) => {
    if (playerRef.current && isCurrentPost) {
      playerRef.current.currentTime = Math.max(0, Math.min(time, playerRef.current.duration || 0))
    }
  }, [isCurrentPost])

  // Set volume for this player
  const setPlayerVolume = useCallback((volume: number) => {
    if (playerRef.current) {
      const clampedVolume = Math.max(0, Math.min(1, volume))
      playerRef.current.volume = clampedVolume
      if (isCurrentPost) {
        setVolume(clampedVolume)
      }
    }
  }, [isCurrentPost, setVolume])

  // Set playback rate for this player
  const setPlayerPlaybackRate = useCallback((rate: number) => {
    if (playerRef.current) {
      playerRef.current.playbackRate = rate
      if (isCurrentPost) {
        setPlaybackRate(rate)
      }
    }
  }, [isCurrentPost, setPlaybackRate])

  // Mute/unmute this player
  const setPlayerMuted = useCallback((muted: boolean) => {
    if (playerRef.current) {
      playerRef.current.muted = muted
      if (isCurrentPost) {
        setMuted(muted)
      }
    }
  }, [isCurrentPost, setMuted])

  // Get player element ref for external use
  const getPlayerRef = useCallback(() => playerRef.current, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current)
      }
    }
  }, [])

  return {
    playerState: isCurrentPost ? playerState : {
      currentPostId: postId,
      isPlaying: false,
      isMuted: true,
      volume: 0.8,
      currentTime: 0,
      duration: 0,
      isBuffering: false,
      playbackRate: 1,
    },
    play,
    pause,
    toggleMute: () => setPlayerMuted(!playerState.isMuted),
    setVolume: setPlayerVolume,
    seek,
    setPlaybackRate: setPlayerPlaybackRate,
    // Additional utilities
    playerRef,
    isCurrentPost,
    getPlayerRef,
    playPost: () => playPost(postId),
  }
}
