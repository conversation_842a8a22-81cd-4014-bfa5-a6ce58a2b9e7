import type { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { randomBytes } from 'crypto'

import type { UserType } from '@/types/auth'

export const fakeUsers: UserType[] = [
  {
    id: '1',
    email: '<EMAIL>',
    username: 'demo_user',
    password: '123456',
    firstName: 'Demo',
    lastName: 'User',
    role: 'Admin',
    token:
      'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************.9uS1E8eoj3pvW-1gxnOfaxP7uTn5mJ90Hwa37kXI7UlCL_DHfwDj2-W9K3o5Yw7EP4Mtf_bSHd4N2mqRnQRvHQ',
  },
]

export const options: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: {
          label: 'Email:',
          type: 'text',
          placeholder: 'Enter your username',
        },
        password: {
          label: 'Password',
          type: 'password',
        },
      },
      async authorize(credentials, req) {
        const filteredUser = fakeUsers.find((user) => {
          return user.email === credentials?.email && user.password === credentials?.password
        })
        if (filteredUser) {
          return filteredUser
        } else {
          throw new Error('Email or Password is not valid')
        }
      },
    }),
  ],
  secret: 'kvwLrfri/MBznUCofIoRH9+NvGu6GqvVdqO3mor1GuA=',
  pages: {
    signIn: '/auth/login',
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      return true
    },
    session: ({ session, token }) => {
      session.user = {
        email: '<EMAIL>',
        name: 'Test User',
      }
      return Promise.resolve(session)
    },
  },
  session: {
    maxAge: 24 * 60 * 60 * 1000,
    generateSessionToken: () => {
      return randomBytes(32).toString('hex')
    },
  },
}
