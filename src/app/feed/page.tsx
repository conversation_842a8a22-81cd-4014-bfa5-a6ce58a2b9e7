"use client"

import { useState } from 'react'
import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { FeedType, InteractionData } from '@/types/feed'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function FeedPage() {
  const [currentFeedType, setCurrentFeedType] = useState<FeedType>(FeedType.DISCOVER)

  const handleItemChange = (item: any, index: number) => {
    console.log('Current item changed:', { item: item.post.title, index })
  }

  const handleInteraction = (interaction: InteractionData) => {
    console.log('User interaction:', interaction)
  }

  return (
    <div className="relative h-screen bg-black">
      {/* Back button */}
      <div className="absolute top-4 left-4 z-30">
        <Link href="/">
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </Link>
      </div>

      {/* Feed Type Selector */}
      <div className="absolute top-4 right-4 z-30 flex space-x-2">
        {Object.values(FeedType).map((feedType) => (
          <Button
            key={feedType}
            variant={currentFeedType === feedType ? "default" : "ghost"}
            size="sm"
            onClick={() => setCurrentFeedType(feedType)}
            className={
              currentFeedType === feedType 
                ? "bg-hvppy-500 text-white" 
                : "text-white hover:bg-white/10"
            }
          >
            {feedType.charAt(0).toUpperCase() + feedType.slice(1)}
          </Button>
        ))}
      </div>

      {/* Main Feed */}
      <VerticalFeedContainer
        feedType={currentFeedType}
        onItemChange={handleItemChange}
        onInteraction={handleInteraction}
        autoPlay={true}
      />

      {/* Instructions overlay */}
      <div className="absolute bottom-4 left-4 right-4 z-20 bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white text-sm">
        <h3 className="font-semibold mb-2">Feed Controls:</h3>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>• Scroll or swipe to navigate</div>
          <div>• Arrow keys for navigation</div>
          <div>• Space to play/pause</div>
          <div>• R to refresh feed</div>
          <div>• M to clear mood filters</div>
          <div>• Click dots to jump to item</div>
        </div>
      </div>
    </div>
  )
}
