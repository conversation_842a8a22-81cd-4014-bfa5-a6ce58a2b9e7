//types
import type { Demo, FeatureType } from './types'

// images
import homeApp from '@/assets/images/demo/landing/home-app.jpg'
import homeSaas from '@/assets/images/demo/landing/home-saas.jpg'
import homeSaas2 from '@/assets/images/demo/landing/home-saas2.jpg'
import homeStartup from '@/assets/images/demo/landing/home-startup.jpg'
import homeSoftware from '@/assets/images/demo/landing/home-software.jpg'
import homeAgency from '@/assets/images/demo/landing/home-agency.jpg'
import homeCoworking from '@/assets/images/demo/landing/home-coworking.jpg'
import homeCrypto from '@/assets/images/demo/landing/home-crypto.jpg'
import homeMarketing from '@/assets/images/demo/landing/home-marketing.jpg'
import homePortfolio from '@/assets/images/demo/landing/home-portfolio.jpg'

import company from '@/assets/images/demo/pages/company.jpg'
import contact from '@/assets/images/demo/pages/contact.jpg'
import career from '@/assets/images/demo/pages/career.jpg'
import blog from '@/assets/images/demo/pages/blog.jpg'
import blogPost from '@/assets/images/demo/pages/blog-post.jpg'
import portfolioGrid from '@/assets/images/demo/pages/portfolio-grid.jpg'
import portfolioMasonry from '@/assets/images/demo/pages/portfolio-masonry.jpg'
import portfolioItem from '@/assets/images/demo/pages/portfolio-item.jpg'
import pricing from '@/assets/images/demo/pages/pricing.jpg'
import help from '@/assets/images/demo/pages/help.jpg'

import authLogin from '@/assets/images/demo/pages/auth-login.jpg'
import authSignup from '@/assets/images/demo/pages/auth-signup.jpg'
import authPassword from '@/assets/images/demo/pages/auth-password.jpg'
import authConfirm from '@/assets/images/demo/pages/auth-confirm.jpg'

const landings: Demo[] = [
  {
    image: homeApp,
    url: '/landing/app',
    name: 'Mobile App',
  },
  {
    image: homeSaas,
    url: '/landing/sass-modern',
    name: 'Saas Modern',
  },
  {
    image: homeSaas2,
    url: '/landing/sass-classic',
    name: 'Saas Classic',
  },
  {
    image: homeStartup,
    url: '/landing/startup',
    name: 'Startup',
  },
  {
    image: homeSoftware,
    url: '/landing/software',
    name: 'Software',
  },
  {
    image: homeAgency,
    url: '/landing/agency',
    name: 'Agency',
  },
  {
    image: homeCoworking,
    url: '/landing/coworking',
    name: 'Co-Working',
  },
  {
    image: homeCrypto,
    url: '/landing/crypto',
    name: 'Crypto',
  },
  {
    image: homeMarketing,
    url: '/landing/marketing',
    name: 'Marketing',
  },
  {
    image: homePortfolio,
    url: '/landing/portfolio',
    name: 'Portfolio',
  },
]

const secondaryPages: Demo[] = [
  {
    image: company,
    url: '/pages/company',
    name: 'Company',
  },
  {
    image: contact,
    url: '/contact-us',
    name: 'Contact',
  },
  {
    image: career,
    url: '/pages/career',
    name: 'Career',
  },
  {
    image: blog,
    url: '/pages/blogs/list',
    name: 'Blog',
  },
  {
    image: blogPost,
    url: '/pages/blogs/post',
    name: 'Blog-Post',
  },
  {
    image: portfolioGrid,
    url: '/pages/portfolio/grid',
    name: 'Portfolio Grid',
  },
  {
    image: portfolioMasonry,
    url: '/pages/portfolio/masonry',
    name: 'Portfolio Masonry',
  },
  {
    image: portfolioItem,
    url: '/pages/portfolio/item',
    name: 'Portfolio-item',
  },
  {
    image: pricing,
    url: '/pages/pricing',
    name: 'Pricing',
  },
  {
    image: help,
    url: '/pages/help',
    name: 'Help',
  },
]

const accountPages: Demo[] = [
  {
    image: authLogin,
    url: 'auth/login',
    name: 'Login',
  },
  {
    image: authSignup,
    url: 'auth/register',
    name: 'SignUp',
  },
  {
    image: authPassword,
    url: 'auth/forgot-password',
    name: 'Forget Password',
  },
  {
    image: authConfirm,
    url: 'auth/confirm-account',
    name: 'Confirm Account',
  },
]

const features: FeatureType[] = [
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M8,2.5 C7.********,2.5 6.75,3.******** 6.75,3.75 L6.75,20.25 C6.75,20.9403559 7.********,21.5 8,21.5 L16,21.5 C16.6903559,21.5 17.25,20.9403559 17.25,20.25 L17.25,3.75 C17.25,3.******** 16.6903559,2.5 16,2.5 L8,2.5 Z"
            id="Combined-Shape"
            fill="currentcolor"
            opacity="0.3"
          />
          <path
            d="M8,2.5 C7.********,2.5 6.75,3.******** 6.75,3.75 L6.75,20.25 C6.75,20.9403559 7.********,21.5 8,21.5 L16,21.5 C16.6903559,21.5 17.25,20.9403559 17.25,20.25 L17.25,3.75 C17.25,3.******** 16.6903559,2.5 16,2.5 L8,2.5 Z M8,1 L16,1 C17.5187831,1 18.75,2.23121694 18.75,3.75 L18.75,20.25 C18.75,21.7687831 17.5187831,23 16,23 L8,23 C6.48121694,23 5.25,21.7687831 5.25,20.25 L5.25,3.75 C5.25,2.23121694 6.48121694,1 8,1 Z M9.5,1.75 L14.5,1.75 C14.7761424,1.75 15,1.97385763 15,2.25 L15,3.25 C15,3.52614237 14.7761424,3.75 14.5,3.75 L9.5,3.75 C9.22385763,3.75 9,3.52614237 9,3.25 L9,2.25 C9,1.97385763 9.22385763,1.75 9.5,1.75 Z"
            id="Combined-Shape"
            fill="currentcolor"
          />
        </g>
      </svg>
    ),
    name: 'Fully Responsive',
  },
  {
    icon: (
      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <title>Stockholm-icons / Layout / Layout-top-panel-6</title>
        <desc>Created with Sketch.</desc>
        <g id="Stockholm-icons-/-Layout-/-Layout-top-panel-6" stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <rect id="Rectangle-7-Copy" fill="#335EEA" x={2} y={5} width={19} height={4} rx={1} />
          <rect id="Rectangle-7-Copy-4" fill="#335EEA" opacity="0.3" x={2} y={11} width={19} height={10} rx={1} />
        </g>
      </svg>
    ),
    name: 'Cross-browser compatible',
  },
  {
    icon: (
      <svg width="1em" height="1em" viewBox="0 0 16 16" className="bi bi-bootstrap" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          d="M12 1H4a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3zM4 0a4 4 0 0 0-4 4v8a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4V4a4 4 0 0 0-4-4H4z"
        />
        <path
          fillRule="evenodd"
          d="M8.537 12H5.062V3.545h3.399c1.587 0 2.543.809 2.543 2.11 0 .884-.65 1.675-1.483 1.816v.1c1.143.117 1.904.931 1.904 2.033 0 1.488-1.084 2.396-2.888 2.396zM6.375 4.658v2.467h1.558c1.16 0 1.764-.428 1.764-1.23 0-.78-.569-1.237-1.541-1.237H6.375zm1.898 6.229H6.375V8.162h1.822c1.236 0 1.887.463 1.887 1.348 0 .896-.627 1.377-1.811 1.377z"
        />
      </svg>
    ),
    name: 'Based on latest Bootstrap v5.3.2',
  },

  {
    text: 'S',
    name: 'Sass Powered',
  },
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 Z M12,5.99999664 C8.6862915,6 6,8.6862915 6,12 C6,15.3137085 8.6862915,18 12,18.0000034 L12,5.99999664 Z"
            id="Combined-Shape"
            fill="currentColor"
          />
        </g>
      </svg>
    ),
    name: 'Easy to customize',
  },
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <rect id="Combined-Shape" fill="currentColor" opacity="0.3" x={2} y={2} width={20} height={20} rx={10} />
          <path
            d="M6.16794971,14.5547002 C5.86159725,14.0951715 5.98577112,13.4743022 6.4452998,13.1679497 C6.90482849,12.8615972 7.52569784,12.9857711 7.83205029,13.4452998 C8.9890854,15.1808525 10.3543313,16 12,16 C13.6456687,16 15.0109146,15.1808525 16.1679497,13.4452998 C16.4743022,12.9857711 17.0951715,12.8615972 17.5547002,13.1679497 C18.0142289,13.4743022 18.1384028,14.0951715 17.8320503,14.5547002 C16.3224187,16.8191475 14.3543313,18 12,18 C9.64566871,18 7.67758127,16.8191475 6.16794971,14.5547002 Z"
            id="Path-56"
            fill="currentColor"
          />
        </g>
      </svg>
    ),
    name: 'Developer Friendly',
  },
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M17.2718029,8.68536757 C16.8932864,8.28319382 16.9124644,7.65031935 17.3146382,7.27180288 C17.7168119,6.89328641 18.3496864,6.91246442 18.7282029,7.31463817 L22.7282029,11.5646382 C23.0906029,11.9496882 23.0906029,12.5503176 22.7282029,12.9353676 L18.7282029,17.1853676 C18.3496864,17.5875413 17.7168119,17.6067193 17.3146382,17.2282029 C16.9124644,16.8496864 16.8932864,16.2168119 17.2718029,15.8146382 L20.6267538,12.2500029 L17.2718029,8.68536757 Z M6.72819712,8.6853647 L3.37324625,12.25 L6.72819712,15.8146353 C7.10671359,16.2168091 7.08753558,16.8496835 6.68536183,17.2282 C6.28318808,17.6067165 5.65031361,17.5875384 5.27179713,17.1853647 L1.27179713,12.9353647 C0.909397125,12.5503147 0.909397125,11.9496853 1.27179713,11.5646353 L5.27179713,7.3146353 C5.65031361,6.91246155 6.28318808,6.89328354 6.68536183,7.27180001 C7.08753558,7.65031648 7.10671359,8.28319095 6.72819712,8.6853647 Z"
            id="Combined-Shape"
            fill="currentColor"
          />
          <rect
            id="Rectangle-28"
            fill="currentColor"
            opacity="0.3"
            transform="translate(12.000000, 12.000000) rotate(-345.000000) translate(-12.000000, -12.000000) "
            x={11}
            y={4}
            width={2}
            height={16}
            rx={1}
          />
        </g>
      </svg>
    ),
    name: 'Clean & Easy to Understand Code',
  },
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <polygon id="Shape" points="0 0 24 0 24 24 0 24" />
          <rect id="Rectangle-25" fill="currentColor" opacity="0.3" x={2} y={4} width={20} height={16} rx={2} />
          <polygon id="Path" fill="currentColor" opacity="0.3" points="4 20 10.5 11 17 20" />
          <polygon id="Path-Copy" fill="currentColor" points="11 20 15.5 14 20 20" />
          <circle id="Oval-76" fill="currentColor" opacity="0.3" cx="18.5" cy="8.5" r="1.5" />
        </g>
      </svg>
    ),
    name: 'Premium SVG Icons',
  },
  {
    icon: (
      <svg
        className="w-6 h-6 text-primary"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink">
        <g stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <polygon id="Shape" points="0 0 24 0 24 24 0 24" />
          <path
            d="M9.********,16.6476484 C8.90425297,17.0684559 8.27315905,17.1196257 7.85235158,16.7619393 C7.43154411,16.404253 7.38037434,15.773159 7.73806068,15.3523516 L16.2380607,5.35235158 C16.6013618,4.92493855 17.2451015,4.87991302 17.6643638,5.******** L22.1643638,9.******** C22.5771466,9.6195087 22.6143273,10.2515811 22.2474093,10.6643638 C21.8804913,11.0771466 21.2484189,11.1143273 20.8356362,10.7474093 L17.0997854,7.******** L9.********,16.6476484 Z"
            id="Path-94-Copy"
            fill="currentColor"
            opacity="0.3"
            transform="translate(14.999995, 11.000002) rotate(-180.000000) translate(-14.999995, -11.000002) "
          />
          <path
            d="M4.********,17.6476484 C3.90425297,18.0684559 3.27315905,18.1196257 2.85235158,17.7619393 C2.43154411,17.404253 2.38037434,16.773159 2.73806068,16.3523516 L11.2380607,6.35235158 C11.6013618,5.92493855 12.2451015,5.87991302 12.6643638,6.******** L17.1643638,10.2525907 C17.5771466,10.6195087 17.6143273,11.2515811 17.2474093,11.6643638 C16.8804913,12.0771466 16.2484189,12.1143273 15.8356362,11.7474093 L12.0997854,8.******** L4.********,17.6476484 Z"
            id="Path-94"
            fill="currentColor"
            transform="translate(9.999995, 12.000002) rotate(-180.000000) translate(-9.999995, -12.000002) "
          />
        </g>
      </svg>
    ),
    name: 'Free Updates',
  },
]
export { landings, secondaryPages, accountPages, features }
