// types
import { <PERSON><PERSON><PERSON>ber } from './types'

// images
import img1 from '@/assets/images/avatars/img-1.jpg'
import img2 from '@/assets/images/avatars/img-2.jpg'
import img3 from '@/assets/images/avatars/img-3.jpg'
import img4 from '@/assets/images/avatars/img-4.jpg'
import img5 from '@/assets/images/avatars/img-5.jpg'
import img6 from '@/assets/images/avatars/img-6.jpg'
import img7 from '@/assets/images/avatars/img-7.jpg'
import img8 from '@/assets/images/avatars/img-8.jpg'

const teamMembers: TeamMember[] = [
  {
    avatar: img1,
    name: '<PERSON>',
    designation: 'CEO',
  },
  {
    avatar: img2,
    name: '<PERSON><PERSON>',
    designation: 'CTO',
  },
  {
    avatar: img3,
    name: '<PERSON>',
    designation: 'VP, Product Development',
  },
  {
    avatar: img4,
    name: '<PERSON>',
    designation: 'Back-End Developer',
  },
  {
    avatar: img5,
    name: '<PERSON>',
    designation: 'P<PERSON> Developer',
  },
  {
    avatar: img6,
    name: '<PERSON>rraro',
    designation: 'Web Designer',
  },
  {
    avatar: img7,
    name: '<PERSON>',
    designation: 'Graphic Designer',
  },
  {
    avatar: img8,
    name: '<PERSON> Clark',
    designation: 'Web Designer',
  },
  {
    avatar: img2,
    name: 'Lindsay Clark',
    designation: 'Front-End Developer',
  },
  {
    avatar: img4,
    name: 'Ernest Griffith',
    designation: 'PHP Developer',
  },
  {
    avatar: img6,
    name: 'Cecelia Poole',
    designation: 'Back-End Developer',
  },
  {
    avatar: img3,
    name: 'Morris Hall',
    designation: 'Graphic Designer',
  },
]

export { teamMembers }
