import type { StaticImageData } from 'next/image'

import avatar2 from '@/assets/images/avatars/img-2.jpg'
import avatar6 from '@/assets/images/avatars/img-6.jpg'

export type CommentType = {
  name: string
  createdAt: string
  avatar: StaticImageData
  description: string
  reply?: CommentType[]
}

export const userComments: CommentType[] = [
  {
    name: '<PERSON><PERSON>',
    createdAt: '2 days ago',
    avatar: avatar2,
    description: 'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque.',
    reply: [
      {
        name: '<PERSON><PERSON><PERSON>',
        createdAt: '1 day ago',
        avatar: avatar6,
        description: 'Itaque earum rerum hic tenetur sapiente delectus aut reiciendis voluptatibus maiores alias consequatur aut perferendis',
      },
    ],
  },
  {
    name: '<PERSON><PERSON>',
    createdAt: '2 days ago',
    avatar: avatar2,
    description: 'At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque.',
  },
]
