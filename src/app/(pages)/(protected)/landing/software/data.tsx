import { ReactNode } from 'react'

export type Feature = {
  deviceIcon: ReactNode
  deviceName: string
}

const features: Feature[] = [
  {
    deviceIcon: (
      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <title>Stockholm-icons / Devices / Laptop</title>
        <desc>Created with Sketch.</desc>
        <g id="Stockholm-icons-/-Devices-/-Laptop" stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M6,8 L6,16 L18,16 L18,8 L6,8 Z M20,16 L21.381966,16 C21.7607381,16 22.1070012,16.2140024 22.2763932,16.5527864 L22.5,17 C22.6706654,17.3413307 22.5323138,17.7563856 22.190983,17.927051 C22.0950363,17.9750244 21.9892377,18 21.881966,18 L2.11803399,18 C1.73641461,18 1.42705098,17.6906364 1.42705098,17.309017 C1.42705098,17.2017453 1.45202663,17.0959467 1.5,17 L1.7236068,16.5527864 C1.89299881,16.2140024 2.23926193,16 2.61803399,16 L4,16 L4,8 C4,6.8954305 4.8954305,6 6,6 L18,6 C19.1045695,6 20,6.8954305 20,8 L20,16 Z"
            id="Combined-Shape"
            fill="#335EEA"
          />
          <polygon id="Combined-Shape" fill="#335EEA" opacity="0.3" points="6 8 6 16 18 16 18 8" />
        </g>
      </svg>
    ),
    deviceName: 'Windows',
  },
  {
    deviceIcon: (
      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <title>Stockholm-icons / Devices / Laptop-macbook</title>
        <desc>Created with Sketch.</desc>
        <g id="Stockholm-icons-/-Devices-/-Laptop-macbook" stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M5,6 L19,6 C19.5522847,6 20,6.44771525 20,7 L20,17 L4,17 L4,7 C4,6.44771525 4.44771525,6 5,6 Z"
            id="Combined-Shape"
            fill="#335EEA"
          />
          <rect id="Rectangle" fill="#335EEA" opacity="0.3" x={1} y={18} width={22} height={1} rx="0.5" />
        </g>
      </svg>
    ),
    deviceName: 'Mac',
  },
  {
    deviceIcon: (
      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <title>Stockholm-icons / Layout / Layout-top-panel-6</title>
        <desc>Created with Sketch.</desc>
        <g id="Stockholm-icons-/-Layout-/-Layout-top-panel-6" stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <rect id="Rectangle-7-Copy" fill="#335EEA" x={2} y={5} width={19} height={4} rx={1} />
          <rect id="Rectangle-7-Copy-4" fill="#335EEA" opacity="0.3" x={2} y={11} width={19} height={10} rx={1} />
        </g>
      </svg>
    ),
    deviceName: 'Browser',
  },
  {
    deviceIcon: (
      <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <title>Stockholm-icons / Devices / Android</title>
        <desc>Created with Sketch.</desc>
        <g id="Stockholm-icons-/-Devices-/-Android" stroke="none" strokeWidth={1} fill="none" fillRule="evenodd">
          <rect id="bound" x={0} y={0} width={24} height={24} />
          <path
            d="M7.5,4 L7.5,19 L16.5,19 L16.5,4 L7.5,4 Z M7.71428571,2 L16.2857143,2 C17.2324881,2 18,2.8954305 18,4 L18,20 C18,21.1045695 17.2324881,22 16.2857143,22 L7.71428571,22 C6.76751186,22 6,21.1045695 6,20 L6,4 C6,2.8954305 6.76751186,2 7.71428571,2 Z"
            id="Combined-Shape"
            fill="#335EEA"
          />
          <polygon id="Combined-Shape" fill="#335EEA" opacity="0.3" points="7.5 4 7.5 19 16.5 19 16.5 4" />
        </g>
      </svg>
    ),
    deviceName: 'Mobile',
  },
]

export { features }
