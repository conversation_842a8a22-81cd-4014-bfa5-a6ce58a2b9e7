{"name": "hvppy-central", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "tsc && next build", "start": "next start", "lint": "next lint", "format": "prettier --write 'src/**/*.{ts,tsx,js,jsx}'"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@iconify/react": "^5.0.2", "aos": "^2.3.4", "bootstrap": "5.3.2", "clsx": "^2.1.1", "glightbox": "^3.3.0", "google-map-react": "^2.2.1", "jarallax": "^2.2.1", "next": "14.2.5", "next-auth": "^4.24.7", "nextjs-toploader": "^1.6.12", "react": "^18.3.1", "react-bootstrap": "^2.10.4", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "react-responsive-masonry": "^2.3.0", "react-text-typist": "^1.1.8", "sass": "^1.77.8", "swiper": "^11.1.9", "yup": "^1.4.0"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/google-map-react": "^2.1.10", "@types/node": "^22.3.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-responsive-masonry": "^2.1.3", "eslint": "^8", "eslint-config-next": "14.2.5", "prettier": "^3.3.3", "typescript": "^5.5.4"}}